# Galaxy Vue

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Vue 3](https://img.shields.io/badge/Vue-3.x-brightgreen.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)

Galaxy Vue is a modern Vue 3 component library built on top of Element Plus, providing enhanced components and advanced table functionality.

## 📦 Packages

- **[@galaxy-vue/ui](./packages/ui)** - Enhanced UI components based on Element Plus
- **[@galaxy-vue/table](./packages/table)** - Advanced table components with AG Grid and Excel export

## 🚀 Features

### @galaxy-vue/ui
- 🎨 Based on Element Plus with custom enhancements
- 🔧 TypeScript support out of the box
- 📱 Responsive design
- 🎯 Easy to use and customize

### @galaxy-vue/table
- 📊 Powerful data grid with AG Grid Community
- 📈 Excel import/export functionality
- 🔍 Advanced filtering and sorting
- 📱 Responsive table design
- 🎨 Customizable themes

## 📖 Quick Start

### Installation

```bash
# Install UI components
npm install @galaxy-vue/ui

# Install table components
npm install @galaxy-vue/table
```

### Usage

#### UI Components

```vue
<template>
  <div>
    <GButton type="primary">Click me</GButton>
    <GCard header="Card Title">
      <p>Card content</p>
    </GCard>
  </div>
</template>

<script setup>
import { GButton, GCard } from '@galaxy-vue/ui'
</script>
```

#### Table Components

```vue
<template>
  <div>
    <GTable
      :columnDefs="columns"
      :rowData="data"
      height="400px"
    />
    <GExcelExport
      :data="data"
      :columns="columns"
      fileName="export.xlsx"
    />
  </div>
</template>

<script setup>
import { GTable, GExcelExport } from '@galaxy-vue/table'

const columns = [
  { field: 'name', headerName: 'Name' },
  { field: 'age', headerName: 'Age' },
  { field: 'email', headerName: 'Email' }
]

const data = [
  { name: 'John', age: 30, email: '<EMAIL>' },
  { name: 'Jane', age: 25, email: '<EMAIL>' }
]
</script>
```

## 🛠️ Development

### Prerequisites

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### Setup

```bash
# Clone the repository
git clone https://github.com/your-username/galaxy-vue.git
cd galaxy-vue

# Install dependencies
pnpm install

# Start development server for UI components
pnpm dev

# Build all packages
pnpm build
```

### Project Structure

```
galaxy-vue/
├── packages/
│   ├── ui/                 # @galaxy-vue/ui
│   │   ├── src/
│   │   │   ├── components/
│   │   │   └── index.ts
│   │   └── package.json
│   └── table/              # @galaxy-vue/table
│       ├── src/
│       │   ├── components/
│       │   ├── utils/
│       │   └── index.ts
│       └── package.json
├── package.json
├── pnpm-workspace.yaml
└── lerna.json
```

## 📄 License

MIT License - see the [LICENSE.md](./LICENSE.md) file for details.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/galaxy-vue/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/galaxy-vue/discussions)
