{"exposed": {"change": "Change to a specific theme.", "toggle": "Toggle between two themes.", "cycle": "Cycle between all or a subset of themes.", "computedThemes": "Object containing all parsed theme definitions.", "current": "Current theme object.", "global": "Reference to the global theme instance.", "isDisabled": "Indicates if theming is disabled.", "name": "Name of current theme.", "prefix": "**FOR INTERNAL USE ONLY**", "scoped": "**FOR INTERNAL USE ONLY**", "styles": "**FOR INTERNAL USE ONLY**", "themeClasses": "**FOR INTERNAL USE ONLY**", "themes": "Raw theme definitions.", "unimportant": "Generate utility classes without the !important declaration.", "utilities": "**FOR INTERNAL USE ONLY**"}}