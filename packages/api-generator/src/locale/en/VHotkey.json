{"props": {"keys": "String representing keyboard shortcuts to display. Supports multiple formats:\n- **Single keys:** `\"k\"`, `\"enter\"`, `\"escape\"`\n- **Key combinations:** `\"ctrl+k\"`, `\"meta+shift+p\"`, `\"alt+arrowup\"`\n- **Sequential actions:** `\"ctrl+k-then-p\"` (use dash for 'then' relationships)\n- **Multiple shortcuts:** `\"ctrl+k meta+p\"` (space-separated for alternative shortcuts)\n\nSupports platform-aware key names like `meta` (becomes Cmd on Mac, Ctrl on PC) and `alt` (becomes Option on Mac).", "displayMode": "Controls how keyboard keys are visually represented. Affects the entire component's appearance:\n- **icon:** Uses SVG icons for keys when appropriate (default)\n- **symbol:** Uses Unicode symbols (⌘, ⌃, ⇧, ⌥) - Allows you to manage presentation of modifier keys with fonts\n- **text:** Uses full text labels (Command, Control, Shift, Alt) - most accessible and descriptive", "keyMap": "Custom key mapping object that defines how individual keys should be displayed. Users can import and modify the exported `hotkeyMap` to create custom configurations. Each key maps to a `PlatformKeyConfig` object with:\n\n```typescript\n{\n  mac?: { symbol?: string, icon?: string, text: string },\n  default: { symbol?: string, icon?: string, text: string }\n}\n```\n\n**Usage Example:**\n```typescript\nimport { hotkeyMap } from 'vuetify/labs/VHotkey'\n\nconst customKeyMap = {\n  ...hotkeyMap,\n  'custom-key': {\n    default: { text: 'Custom', icon: 'custom-icon' },\n    mac: { text: 'Custom', symbol: '⚡' }\n  }\n}\n```\n\nThis enables:\n- **Custom key definitions:** Add support for application-specific keys\n- **Localization:** Override text representations for different languages\n- **Brand customization:** Change how modifier keys appear\n- **Platform-specific styling:** Different representations for Mac vs other platforms\n\nRecommended to set at the application level via component defaults rather than per-instance for consistency.", "platform": "Controls platform-specific rendering behavior for keyboard shortcuts. Accepts three values:\n- **`'auto'` (default):** Automatically detects the user's platform based on user agent and renders appropriately\n- **`'mac'`:** Forces Mac-style rendering (Command symbols, icons, Option key, etc.)\n- **`'pc'`:** Forces PC-style rendering (Ctrl text, Alt key, etc.)\n\nThis is particularly useful for:\n- **Cross-platform testing:** Verify how shortcuts appear on different platforms\n- **Design consistency:** Ensure specific platform rendering in demos and prototypes\n- **Development workflow:** Test platform-specific behaviors without switching devices\n- **Documentation:** Show platform-specific examples in help content", "inline": "Optimizes the component for seamless integration within text content and documentation. Applies compact styling with baseline alignment, constrained height (1lh), and responsive typography that inherits from parent text. Ideal for help documentation, tooltips, and instructional content. When using multiple inline hotkeys in the same paragraph, increase line-height to prevent visual overlap on text wrapping.", "variant": "Controls the visual style and presentation of the hotkey component. Supports standard Vuetify variants plus a special contained variant:\n\n**Standard Variants** (apply styling to individual key elements):\n- **elevated (default):** Raised appearance with shadow, good for standalone hotkey displays\n- **flat:** Solid background without shadow, clean and minimal\n- **tonal:** Subtle tinted background without border, balances visibility with restraint\n- **outlined:** Border-only styling without elevation, lightweight and unobtrusive\n- **text:** Minimal styling with text color emphasis only, blends with content\n- **plain:** No background or border, most subtle option\n\n**Special Variant** (different visual structure):\n- **contained:** Follows MDN's nested `<kbd>` pattern - wraps all keys in a single styled container with unstyled nested elements. Creates a cohesive visual unit that clearly groups related keys together. Cannot be combined with standard variants. Ideal for complex key combinations where you want to show the entire sequence as one unit.", "disabled": "Applies a disabled visual state to the component.", "prefix": "Text to display before the hotkey.", "suffix": "Text to display after the hotkey."}, "slots": {"default": "The default Vue slot. Not supported."}}