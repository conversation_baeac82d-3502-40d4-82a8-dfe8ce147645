{"props": {"absolute": "Applies position: absolute to the component.", "bottom": "Aligns the component towards the bottom.", "collapse": "Puts the toolbar into a collapsed state reducing its maximum width.", "extended": "Use this prop to increase the height of the toolbar _without_ using the `extension` slot for adding content. May be used in conjunction with the **extension-height** prop. When false, will not show extension slot even if content is present.", "extensionHeight": "Specify an explicit height for the `extension` slot.", "flat": "Removes the toolbar's box-shadow.", "floating": "Applies **display: inline-flex** to the component.", "height": "Designates a specific height for the toolbar. Overrides the heights imposed by other props, e.g. **prominent**, **dense**, **extended**, etc.", "image": "Specifies a [v-img](/components/images) as the component's background."}, "slots": {"extension": "Slot positioned directly under the main content of the toolbar. Height of this slot can be set explicitly with the **extension-height** prop. If this slot has no content, the **extended** prop may be used instead.", "image": "Expects the [v-img](/components/images) component. Scoped **props** should be applied with `v-bind=\"props\"`."}, "exposed": {"contentHeight": "The current height of the component's content.", "extensionHeight": "The current height of the component's extension slot."}}