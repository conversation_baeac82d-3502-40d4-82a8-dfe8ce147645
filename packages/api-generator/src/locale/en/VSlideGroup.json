{"props": {"centerActive": "Forces the selected component to be centered.", "contentClass": "Adds classes to the slide group item.", "direction": "Switch between horizontal and vertical modes.", "mobileBreakpoint": "Sets the designated mobile breakpoint for the component.", "nextIcon": "The appended slot when arrows are shown.", "prevIcon": "The prepended slot when arrows are shown.", "showArrows": "Change when the overflow arrow indicators are shown. By **default**, arrows *always* display on Desktop when the container is overflowing. When the container overflows on mobile, arrows are not shown by default. A **show-arrows** value of `true` allows these arrows to show on Mobile if the container overflowing. A value of `desktop` *always* displays arrows on Desktop while a value of `mobile` always displays arrows on Mobile. A value of `always` always displays arrows on Desktop *and* Mobile. Find more information on how to customize breakpoint thresholds on the [breakpoints page](/customizing/breakpoints)."}, "slots": {"next": "The next slot.", "prev": "The prev slot."}, "events": {"change": "Emitted when the component value is changed by user interaction.", "click:prev": "Emitted when the prev is clicked.", "click:next": "Emitted when the next is clicked."}, "exposed": {"focus": "Focus the component.", "hasNext": "Returns true if there are items after current index.", "hasPrev": "Returns true if there are items before current index.", "scrollOffset": "Scroll the component to a given index.", "scrollTo": "Scroll the component to a given index.", "selected": "Get the selected component index."}}