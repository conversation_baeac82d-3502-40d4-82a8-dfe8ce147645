{"props": {"active": "When undefined (default), the component utilizes its default variant, otherwise it will use the activeVariant if active is true, or the baseVariant if active is false.", "activeIcon": "When active is a boolean, this icon is used when active is true.", "activeVariant": "When active is a boolean, this variant is used when active is true.", "baseVariant": "When active is a boolean, this variant is used when active is false.", "hideOverlay": "Hides overlay from being displayed when active or focused.", "iconColor": "Explicit color applied to the icon.", "loading": "Displays circular progress bar in place of the icon.", "readonly": "Puts the button in a readonly state. Cannot be clicked or navigated to by keyboard.", "rotate": "The rotation of the icon in degrees.", "sizes": "An array of tuples that define the button sizes for each named size."}, "events": {"update:active": "Event that is emitted when the active state changes."}}