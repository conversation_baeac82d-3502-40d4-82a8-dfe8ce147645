(function(a,n){typeof exports=="object"&&typeof module<"u"?n(exports,require("element-plus"),require("vue")):typeof define=="function"&&define.amd?define(["exports","element-plus","vue"],n):(a=typeof globalThis<"u"?globalThis:a||self,n(a.GalaxyVueUI={},a.ElementPlus,a.Vue))})(this,function(a,n,o){"use strict";const u=o.defineComponent({name:"GButton",__name:"index",props:{type:{default:""},size:{default:"default"},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},icon:{},round:{type:Boolean,default:!1},circle:{type:Boolean,default:!1},plain:{type:Boolean,default:!1},text:{type:Boolean,default:!1},bg:{type:<PERSON>olean,default:!1},link:{type:<PERSON><PERSON><PERSON>,default:!1},color:{},dark:{type:Boolean,default:!1},autoInsertSpace:{type:Boolean,default:!1}},emits:["click"],setup(l,{emit:t}){const d=t,s=e=>{d("click",e)};return(e,k)=>(o.openBlock(),o.createBlock(o.unref(n.ElButton),{type:e.type,size:e.size,disabled:e.disabled,loading:e.loading,icon:e.icon,round:e.round,circle:e.circle,plain:e.plain,text:e.text,bg:e.bg,link:e.link,color:e.color,dark:e.dark,"auto-insert-space":e.autoInsertSpace,onClick:s},{default:o.withCtx(()=>[o.renderSlot(e.$slots,"default",{},void 0,!0)]),_:3},8,["type","size","disabled","loading","icon","round","circle","plain","text","bg","link","color","dark","auto-insert-space"]))}}),r=(l,t)=>{const d=l.__vccOpts||l;for(const[s,e]of t)d[s]=e;return d},i=r(u,[["__scopeId","data-v-50a670f2"]]),p={class:"g-card__footer"},f=r(o.defineComponent({name:"GCard",__name:"index",props:{header:{},bodyStyle:{},shadow:{default:"always"}},setup(l){return(t,d)=>(o.openBlock(),o.createBlock(o.unref(n.ElCard),{header:t.header,"body-style":t.bodyStyle,shadow:t.shadow,class:"g-card"},o.createSlots({default:o.withCtx(()=>[o.renderSlot(t.$slots,"default",{},void 0,!0)]),_:2},[t.$slots.header?{name:"header",fn:o.withCtx(()=>[o.renderSlot(t.$slots,"header",{},void 0,!0)]),key:"0"}:void 0,t.$slots.footer?{name:"footer",fn:o.withCtx(()=>[o.createElementVNode("div",p,[o.renderSlot(t.$slots,"footer",{},void 0,!0)])]),key:"1"}:void 0]),1032,["header","body-style","shadow"]))}}),[["__scopeId","data-v-c7bb86d4"]]),y=[i,f],c=l=>{l.use(n),y.forEach(t=>{const d=t.name||t.__name||"UnknownComponent";l.component(d,t)})},h={install:c};a.GButton=i,a.GCard=f,a.default=h,a.install=c,Object.defineProperties(a,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
