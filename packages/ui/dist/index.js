import c, { ElButton as p, El<PERSON>ard as y } from "element-plus";
import { defineComponent as d, createBlock as r, openBlock as i, unref as f, withCtx as l, renderSlot as n, createSlots as m, createElementVNode as k } from "vue";
const h = /* @__PURE__ */ d({
  name: "<PERSON>utton",
  __name: "index",
  props: {
    type: { default: "" },
    size: { default: "default" },
    disabled: { type: Boolean, default: !1 },
    loading: { type: Boolean, default: !1 },
    icon: {},
    round: { type: Boolean, default: !1 },
    circle: { type: Boolean, default: !1 },
    plain: { type: Boolean, default: !1 },
    text: { type: Boolean, default: !1 },
    bg: { type: Boolean, default: !1 },
    link: { type: Boolean, default: !1 },
    color: {},
    dark: { type: Boolean, default: !1 },
    autoInsertSpace: { type: Boolean, default: !1 }
  },
  emits: ["click"],
  setup(a, { emit: o }) {
    const t = o, s = (e) => {
      t("click", e);
    };
    return (e, w) => (i(), r(f(p), {
      type: e.type,
      size: e.size,
      disabled: e.disabled,
      loading: e.loading,
      icon: e.icon,
      round: e.round,
      circle: e.circle,
      plain: e.plain,
      text: e.text,
      bg: e.bg,
      link: e.link,
      color: e.color,
      dark: e.dark,
      "auto-insert-space": e.autoInsertSpace,
      onClick: s
    }, {
      default: l(() => [
        n(e.$slots, "default", {}, void 0, !0)
      ]),
      _: 3
    }, 8, ["type", "size", "disabled", "loading", "icon", "round", "circle", "plain", "text", "bg", "link", "color", "dark", "auto-insert-space"]));
  }
}), u = (a, o) => {
  const t = a.__vccOpts || a;
  for (const [s, e] of o)
    t[s] = e;
  return t;
}, B = /* @__PURE__ */ u(h, [["__scopeId", "data-v-50a670f2"]]), b = { class: "g-card__footer" }, g = /* @__PURE__ */ d({
  name: "GCard",
  __name: "index",
  props: {
    header: {},
    bodyStyle: {},
    shadow: { default: "always" }
  },
  setup(a) {
    return (o, t) => (i(), r(f(y), {
      header: o.header,
      "body-style": o.bodyStyle,
      shadow: o.shadow,
      class: "g-card"
    }, m({
      default: l(() => [
        n(o.$slots, "default", {}, void 0, !0)
      ]),
      _: 2
    }, [
      o.$slots.header ? {
        name: "header",
        fn: l(() => [
          n(o.$slots, "header", {}, void 0, !0)
        ]),
        key: "0"
      } : void 0,
      o.$slots.footer ? {
        name: "footer",
        fn: l(() => [
          k("div", b, [
            n(o.$slots, "footer", {}, void 0, !0)
          ])
        ]),
        key: "1"
      } : void 0
    ]), 1032, ["header", "body-style", "shadow"]));
  }
}), v = /* @__PURE__ */ u(g, [["__scopeId", "data-v-c7bb86d4"]]), _ = [
  B,
  v
], C = (a) => {
  a.use(c), _.forEach((o) => {
    const t = o.name || o.__name || "UnknownComponent";
    a.component(t, o);
  });
}, E = {
  install: C
};
export {
  B as GButton,
  v as GCard,
  E as default,
  C as install
};
