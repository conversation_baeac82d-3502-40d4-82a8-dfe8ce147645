import { Component } from 'vue';
interface Props {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | '';
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    loading?: boolean;
    icon?: string | Component;
    round?: boolean;
    circle?: boolean;
    plain?: boolean;
    text?: boolean;
    bg?: boolean;
    link?: boolean;
    color?: string;
    dark?: boolean;
    autoInsertSpace?: boolean;
}
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        default?(_: {}): any;
    };
    refs: {};
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: import('vue').DefineComponent<Props, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    click: (event: MouseEvent) => any;
}, string, import('vue').PublicProps, Readonly<Props> & Readonly<{
    onClick?: ((event: MouseEvent) => any) | undefined;
}>, {
    text: boolean;
    type: "primary" | "success" | "warning" | "danger" | "info" | "text" | "";
    size: "large" | "default" | "small";
    disabled: boolean;
    loading: boolean;
    round: boolean;
    circle: boolean;
    plain: boolean;
    bg: boolean;
    link: boolean;
    dark: boolean;
    autoInsertSpace: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
        $props: __VLS_PropsChildren<S>;
    };
};
type __VLS_PropsChildren<S> = {
    [K in keyof (boolean extends (JSX.ElementChildrenAttribute extends never ? true : false) ? never : JSX.ElementChildrenAttribute)]?: S;
};
