<template>
  <div class="g-table-container">
    <ag-grid-vue
      ref="agGridRef"
      class="ag-theme-alpine"
      :style="{ height: computedHeight }"
      :columnDefs="columnDefs"
      :rowData="rowData"
      :defaultColDef="mergedDefaultColDef"
      :pagination="pagination"
      :paginationPageSize="paginationPageSize"
      :rowSelection="rowSelection"
      :suppressRowClickSelection="suppressRowClickSelection"
      :enableRangeSelection="enableRangeSelection"
      :enableCharts="enableCharts"
      :sideBar="sideBar"
      @grid-ready="onGridReady"
      @selection-changed="onSelectionChanged"
      @cell-clicked="onCellClicked"
      @cell-double-clicked="onCellDoubleClicked"
      @row-clicked="onRowClicked"
      @row-double-clicked="onRowDoubleClicked"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import type { GridApi, ColumnApi, GridReadyEvent } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import type { TableProps, TableColumn, TableData } from '../../types'

defineOptions({
  name: 'GTable'
})

interface Props extends TableProps {}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  theme: 'ag-theme-alpine',
  pagination: true,
  paginationPageSize: 20,
  rowSelection: 'multiple',
  suppressRowClickSelection: false,
  enableRangeSelection: true,
  enableCharts: false,
  sideBar: false,
  defaultColDef: () => ({
    resizable: true,
    sortable: true,
    filter: true,
    minWidth: 100
  })
})

const emit = defineEmits<{
  gridReady: [event: GridReadyEvent]
  selectionChanged: [selectedRows: TableData[]]
  cellClicked: [event: any]
  cellDoubleClicked: [event: any]
  rowClicked: [event: any]
  rowDoubleClicked: [event: any]
}>()

const agGridRef = ref<InstanceType<typeof AgGridVue>>()
const gridApi = ref<GridApi>()
const columnApi = ref<ColumnApi>()

const computedHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  }
  return props.height
})

const mergedDefaultColDef = computed(() => ({
  resizable: true,
  sortable: true,
  filter: true,
  minWidth: 100,
  ...props.defaultColDef
}))

const onGridReady = (event: GridReadyEvent) => {
  gridApi.value = event.api
  columnApi.value = event.columnApi
  emit('gridReady', event)
}

const onSelectionChanged = () => {
  if (gridApi.value) {
    const selectedRows = gridApi.value.getSelectedRows()
    emit('selectionChanged', selectedRows)
  }
}

const onCellClicked = (event: any) => {
  emit('cellClicked', event)
}

const onCellDoubleClicked = (event: any) => {
  emit('cellDoubleClicked', event)
}

const onRowClicked = (event: any) => {
  emit('rowClicked', event)
}

const onRowDoubleClicked = (event: any) => {
  emit('rowDoubleClicked', event)
}

// 暴露方法给父组件
defineExpose({
  getGridApi: () => gridApi.value,
  getColumnApi: () => columnApi.value,
  getSelectedRows: () => gridApi.value?.getSelectedRows() || [],
  exportToCsv: (params?: any) => gridApi.value?.exportDataAsCsv(params),
  sizeColumnsToFit: () => gridApi.value?.sizeColumnsToFit(),
  autoSizeAllColumns: () => {
    if (gridApi.value && columnApi.value) {
      const allColumnIds = columnApi.value.getAllColumns()?.map(col => col.getId()) || []
      columnApi.value.autoSizeColumns(allColumnIds)
    }
  }
})
</script>

<style scoped>
.g-table-container {
  width: 100%;
}

.ag-theme-alpine {
  --ag-font-family: inherit;
}
</style>
