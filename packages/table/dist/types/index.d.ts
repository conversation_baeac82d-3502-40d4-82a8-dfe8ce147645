import { ColDef } from 'ag-grid-community';
export interface TableColumn extends ColDef {
    field: string;
    headerName?: string;
    width?: number;
    minWidth?: number;
    maxWidth?: number;
    resizable?: boolean;
    sortable?: boolean;
    filter?: boolean | string;
    editable?: boolean;
    cellRenderer?: string | any;
    cellEditor?: string | any;
    valueGetter?: (params: any) => any;
    valueSetter?: (params: any) => boolean;
    cellStyle?: any;
    headerClass?: string | string[];
    cellClass?: string | string[] | ((params: any) => string | string[]);
}
export interface ExportOptions {
    fileName?: string;
    sheetName?: string;
    includeHeaders?: boolean;
    onlySelected?: boolean;
    columnKeys?: string[];
    processCellCallback?: (params: any) => any;
    processHeaderCallback?: (params: any) => string;
}
export interface TableData {
    [key: string]: any;
}
export interface TableProps {
    columnDefs: TableColumn[];
    rowData: TableData[];
    height?: string | number;
    theme?: string;
    pagination?: boolean;
    paginationPageSize?: number;
    rowSelection?: 'single' | 'multiple';
    suppressRowClickSelection?: boolean;
    enableRangeSelection?: boolean;
    enableCharts?: boolean;
    sideBar?: boolean | any;
    defaultColDef?: Partial<TableColumn>;
}
export interface ExcelExportProps {
    data: TableData[];
    columns: TableColumn[];
    fileName?: string;
    sheetName?: string;
    autoWidth?: boolean;
}
