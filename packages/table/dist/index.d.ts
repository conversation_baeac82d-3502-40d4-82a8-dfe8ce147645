import { App } from 'vue';
import { default as GTable } from './components/GTable/index.vue';
import { default as GExcelExport } from './components/GExcelExport/index.vue';
import { exportToExcel } from './utils/excel';
import { TableColumn, ExportOptions } from './types';
declare const install: (app: App) => void;
export { GTable, GExcelExport, exportToExcel, type TableColumn, type ExportOptions, install };
declare const _default: {
    install: (app: App) => void;
};
export default _default;
