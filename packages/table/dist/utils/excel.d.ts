import { TableData, TableColumn, ExportOptions } from '../types';
/**
 * 导出数据到 Excel 文件
 * @param data 要导出的数据
 * @param columns 列定义
 * @param options 导出选项
 */
export declare function exportToExcel(data: TableData[], columns: TableColumn[], options?: ExportOptions): void;
/**
 * 从文件读取 Excel 数据
 * @param file 文件对象
 * @param sheetName 工作表名称，默认读取第一个
 * @returns Promise<TableData[]>
 */
export declare function readExcelFile(file: File, sheetName?: string): Promise<TableData[]>;
