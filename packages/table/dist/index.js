import { defineComponent as _, ref as v, computed as S, createElement<PERSON>lock as C, openBlock as b, createVNode as D, unref as R, normalizeStyle as E, createElementVNode as N, normalizeClass as B, createTextVNode as z, toDisplayString as T } from "vue";
import { AgGridVue as G } from "ag-grid-vue3";
import * as g from "xlsx";
const A = { class: "g-table-container" }, P = /* @__PURE__ */ _({
  name: "GTable",
  __name: "index",
  props: {
    columnDefs: {},
    rowData: {},
    height: { default: "400px" },
    theme: { default: "ag-theme-alpine" },
    pagination: { type: Boolean, default: !0 },
    paginationPageSize: { default: 20 },
    rowSelection: { default: "multiple" },
    suppressRowClickSelection: { type: Boolean, default: !1 },
    enableRangeSelection: { type: Boolean, default: !0 },
    enableCharts: { type: Boolean, default: !1 },
    sideBar: { type: Boolean, default: !1 },
    defaultColDef: { default: () => ({
      resizable: !0,
      sortable: !0,
      filter: !0,
      minWidth: 100
    }) }
  },
  emits: ["gridReady", "selectionChanged", "cellClicked", "cellDoubleClicked", "rowClicked", "rowDoubleClicked"],
  setup(r, { expose: s, emit: d }) {
    const o = r, l = d, i = v(), a = v(), n = S(() => typeof o.height == "number" ? `${o.height}px` : o.height), c = S(() => ({
      resizable: !0,
      sortable: !0,
      filter: !0,
      minWidth: 100,
      ...o.defaultColDef
    })), p = (e) => {
      a.value = e.api, l("gridReady", e);
    }, x = () => {
      if (a.value) {
        const e = a.value.getSelectedRows();
        l("selectionChanged", e);
      }
    }, f = (e) => {
      l("cellClicked", e);
    }, k = (e) => {
      l("cellDoubleClicked", e);
    }, m = (e) => {
      l("rowClicked", e);
    }, w = (e) => {
      l("rowDoubleClicked", e);
    };
    return s({
      getGridApi: () => a.value,
      getSelectedRows: () => {
        var e;
        return ((e = a.value) == null ? void 0 : e.getSelectedRows()) || [];
      },
      exportToCsv: (e) => {
        var t;
        return (t = a.value) == null ? void 0 : t.exportDataAsCsv(e);
      },
      sizeColumnsToFit: () => {
        var e;
        return (e = a.value) == null ? void 0 : e.sizeColumnsToFit();
      },
      autoSizeAllColumns: () => {
        var e;
        if (a.value) {
          const t = ((e = a.value.getColumns()) == null ? void 0 : e.map((u) => u.getId())) || [];
          a.value.autoSizeColumns(t);
        }
      }
    }), (e, t) => (b(), C("div", A, [
      D(R(G), {
        ref_key: "agGridRef",
        ref: i,
        class: "ag-theme-alpine",
        style: E({ height: n.value }),
        columnDefs: e.columnDefs,
        rowData: e.rowData,
        defaultColDef: c.value,
        pagination: e.pagination,
        paginationPageSize: e.paginationPageSize,
        rowSelection: e.rowSelection,
        suppressRowClickSelection: e.suppressRowClickSelection,
        enableRangeSelection: e.enableRangeSelection,
        enableCharts: e.enableCharts,
        sideBar: e.sideBar,
        onGridReady: p,
        onSelectionChanged: x,
        onCellClicked: f,
        onCellDoubleClicked: k,
        onRowClicked: m,
        onRowDoubleClicked: w
      }, null, 8, ["style", "columnDefs", "rowData", "defaultColDef", "pagination", "paginationPageSize", "rowSelection", "suppressRowClickSelection", "enableRangeSelection", "enableCharts", "sideBar"])
    ]));
  }
}), y = (r, s) => {
  const d = r.__vccOpts || r;
  for (const [o, l] of s)
    d[o] = l;
  return d;
}, H = /* @__PURE__ */ y(P, [["__scopeId", "data-v-95dbd33e"]]);
function I(r, s, d = {}) {
  const {
    fileName: o = "export.xlsx",
    sheetName: l = "Sheet1",
    includeHeaders: i = !0,
    columnKeys: a,
    processCellCallback: n,
    processHeaderCallback: c
  } = d, p = a ? s.filter((t) => a.includes(t.field)) : s, x = i ? p.map((t) => c ? c({ column: t }) : t.headerName || t.field) : [], f = r.map((t) => p.map((u) => {
    let h = t[u.field];
    return n && (h = n({
      data: t,
      column: u,
      value: h
    })), u.valueGetter && (h = u.valueGetter({ data: t })), h;
  })), k = i ? [x, ...f] : f, m = g.utils.aoa_to_sheet(k), w = p.map((t) => ({
    wch: Math.max(
      (t.headerName || t.field).length,
      t.width ? t.width / 10 : 15
    )
  }));
  m["!cols"] = w;
  const e = g.utils.book_new();
  g.utils.book_append_sheet(e, m, l), g.writeFile(e, o);
}
const V = { class: "g-excel-export" }, W = ["disabled"], F = {
  key: 0,
  class: "loading-icon"
}, O = {
  key: 1,
  class: "export-icon"
}, $ = /* @__PURE__ */ _({
  name: "GExcelExport",
  __name: "index",
  props: {
    buttonText: { default: "导出 Excel" },
    loadingText: { default: "导出中..." },
    buttonClass: { default: "g-export-button" },
    disabled: { type: Boolean, default: !1 },
    exportOptions: { default: () => ({}) },
    data: {},
    columns: {},
    fileName: { default: "export.xlsx" },
    sheetName: { default: "Sheet1" },
    autoWidth: { type: Boolean, default: !0 }
  },
  emits: ["beforeExport", "afterExport"],
  setup(r, { expose: s, emit: d }) {
    const o = r, l = d, i = v(!1), a = async () => {
      if (!(i.value || o.disabled))
        try {
          i.value = !0, l("beforeExport");
          const n = {
            fileName: o.fileName,
            sheetName: o.sheetName,
            includeHeaders: !0,
            ...o.exportOptions
          };
          await new Promise((c) => setTimeout(c, 100)), I(o.data, o.columns, n), l("afterExport", !0);
        } catch (n) {
          console.error("Export failed:", n), l("afterExport", !1, n);
        } finally {
          i.value = !1;
        }
    };
    return s({
      export: a
    }), (n, c) => (b(), C("div", V, [
      N("button", {
        class: B(n.buttonClass),
        disabled: n.disabled || i.value,
        onClick: a
      }, [
        i.value ? (b(), C("span", F, "⏳")) : (b(), C("span", O, "📊")),
        z(" " + T(i.value ? n.loadingText : n.buttonText), 1)
      ], 10, W)
    ]));
  }
}), X = /* @__PURE__ */ y($, [["__scopeId", "data-v-faaaa3c6"]]), K = [
  H,
  X
], L = (r) => {
  K.forEach((s) => {
    const d = s.name || s.__name || "UnknownComponent";
    r.component(d, s);
  });
}, j = {
  install: L
};
export {
  X as GExcelExport,
  H as GTable,
  j as default,
  I as exportToExcel,
  L as install
};
