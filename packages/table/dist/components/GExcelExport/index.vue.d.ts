import { ExcelExportProps, ExportOptions } from '../../types';
interface Props extends ExcelExportProps {
    buttonText?: string;
    loadingText?: string;
    buttonClass?: string;
    disabled?: boolean;
    exportOptions?: Partial<ExportOptions>;
}
declare const _default: import('vue').DefineComponent<Props, {
    export: () => Promise<void>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    beforeExport: () => any;
    afterExport: (success: boolean, error?: Error | undefined) => any;
}, string, import('vue').PublicProps, Readonly<Props> & Readonly<{
    onBeforeExport?: (() => any) | undefined;
    onAfterExport?: ((success: boolean, error?: Error | undefined) => any) | undefined;
}>, {
    fileName: string;
    sheetName: string;
    buttonText: string;
    loadingText: string;
    buttonClass: string;
    disabled: boolean;
    exportOptions: Partial<ExportOptions>;
    autoWidth: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLDivElement>;
export default _default;
