import { GridApi, GridReadyEvent } from 'ag-grid-community';
import { TableProps, TableData } from '../../types';
interface Props extends TableProps {
}
declare const _default: import('vue').DefineComponent<Props, {
    getGridApi: () => GridApi<any> | undefined;
    getSelectedRows: () => any[];
    exportToCsv: (params?: any) => void | undefined;
    sizeColumnsToFit: () => void | undefined;
    autoSizeAllColumns: () => void;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    gridReady: (event: GridReadyEvent<any, any>) => any;
    selectionChanged: (selectedRows: TableData[]) => any;
    cellClicked: (event: any) => any;
    cellDoubleClicked: (event: any) => any;
    rowClicked: (event: any) => any;
    rowDoubleClicked: (event: any) => any;
}, string, import('vue').PublicProps, Readonly<Props> & Readonly<{
    onGridReady?: ((event: GridReadyEvent<any, any>) => any) | undefined;
    onSelectionChanged?: ((selectedRows: TableData[]) => any) | undefined;
    onCellClicked?: ((event: any) => any) | undefined;
    onCellDoubleClicked?: ((event: any) => any) | undefined;
    onRowClicked?: ((event: any) => any) | undefined;
    onRowDoubleClicked?: ((event: any) => any) | undefined;
}>, {
    height: string | number;
    theme: string;
    pagination: boolean;
    paginationPageSize: number;
    rowSelection: "single" | "multiple";
    suppressRowClickSelection: boolean;
    enableRangeSelection: boolean;
    enableCharts: boolean;
    sideBar: boolean | any;
    defaultColDef: Partial<import('../..').TableColumn>;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {
    agGridRef: import('vue').ShallowUnwrapRef<{
        api: import('vue').Ref<GridApi<any> | undefined, GridApi<any> | undefined>;
    }> | null;
}, HTMLDivElement>;
export default _default;
