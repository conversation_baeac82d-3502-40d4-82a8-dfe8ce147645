(function(d,t){typeof exports=="object"&&typeof module<"u"?t(exports,require("vue"),require("ag-grid-vue3"),require("xlsx")):typeof define=="function"&&define.amd?define(["exports","vue","ag-grid-vue3","xlsx"],t):(d=typeof globalThis<"u"?globalThis:d||self,t(d.GalaxyVueTable={},d.Vue,d.AgGridVue3,d.XLSX))})(this,function(d,t,B,R){"use strict";function T(l){const n=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(l){for(const i in l)if(i!=="default"){const a=Object.getOwnPropertyDescriptor(l,i);Object.defineProperty(n,i,a.get?a:{enumerable:!0,get:()=>l[i]})}}return n.default=l,Object.freeze(n)}const g=T(R),N={class:"g-table-container"},G=t.defineComponent({name:"GTable",__name:"index",props:{columnDefs:{},rowData:{},height:{default:"400px"},theme:{default:"ag-theme-alpine"},pagination:{type:Boolean,default:!0},paginationPageSize:{default:20},rowSelection:{default:"multiple"},suppressRowClickSelection:{type:Boolean,default:!1},enableRangeSelection:{type:Boolean,default:!0},enableCharts:{type:Boolean,default:!1},sideBar:{type:Boolean,default:!1},defaultColDef:{default:()=>({resizable:!0,sortable:!0,filter:!0,minWidth:100})}},emits:["gridReady","selectionChanged","cellClicked","cellDoubleClicked","rowClicked","rowDoubleClicked"],setup(l,{expose:n,emit:i}){const a=l,r=i,u=t.ref(),s=t.ref(),c=t.computed(()=>typeof a.height=="number"?`${a.height}px`:a.height),p=t.computed(()=>({resizable:!0,sortable:!0,filter:!0,minWidth:100,...a.defaultColDef})),m=e=>{s.value=e.api,r("gridReady",e)},x=()=>{if(s.value){const e=s.value.getSelectedRows();r("selectionChanged",e)}},h=e=>{r("cellClicked",e)},k=e=>{r("cellDoubleClicked",e)},b=e=>{r("rowClicked",e)},w=e=>{r("rowDoubleClicked",e)};return n({getGridApi:()=>s.value,getSelectedRows:()=>{var e;return((e=s.value)==null?void 0:e.getSelectedRows())||[]},exportToCsv:e=>{var o;return(o=s.value)==null?void 0:o.exportDataAsCsv(e)},sizeColumnsToFit:()=>{var e;return(e=s.value)==null?void 0:e.sizeColumnsToFit()},autoSizeAllColumns:()=>{var e;if(s.value){const o=((e=s.value.getColumns())==null?void 0:e.map(f=>f.getId()))||[];s.value.autoSizeColumns(o)}}}),(e,o)=>(t.openBlock(),t.createElementBlock("div",N,[t.createVNode(t.unref(B.AgGridVue),{ref_key:"agGridRef",ref:u,class:"ag-theme-alpine",style:t.normalizeStyle({height:c.value}),columnDefs:e.columnDefs,rowData:e.rowData,defaultColDef:p.value,pagination:e.pagination,paginationPageSize:e.paginationPageSize,rowSelection:e.rowSelection,suppressRowClickSelection:e.suppressRowClickSelection,enableRangeSelection:e.enableRangeSelection,enableCharts:e.enableCharts,sideBar:e.sideBar,onGridReady:m,onSelectionChanged:x,onCellClicked:h,onCellDoubleClicked:k,onRowClicked:b,onRowDoubleClicked:w},null,8,["style","columnDefs","rowData","defaultColDef","pagination","paginationPageSize","rowSelection","suppressRowClickSelection","enableRangeSelection","enableCharts","sideBar"])]))}}),S=(l,n)=>{const i=l.__vccOpts||l;for(const[a,r]of n)i[a]=r;return i},_=S(G,[["__scopeId","data-v-95dbd33e"]]);function y(l,n,i={}){const{fileName:a="export.xlsx",sheetName:r="Sheet1",includeHeaders:u=!0,columnKeys:s,processCellCallback:c,processHeaderCallback:p}=i,m=s?n.filter(o=>s.includes(o.field)):n,x=u?m.map(o=>p?p({column:o}):o.headerName||o.field):[],h=l.map(o=>m.map(f=>{let C=o[f.field];return c&&(C=c({data:o,column:f,value:C})),f.valueGetter&&(C=f.valueGetter({data:o})),C})),k=u?[x,...h]:h,b=g.utils.aoa_to_sheet(k),w=m.map(o=>({wch:Math.max((o.headerName||o.field).length,o.width?o.width/10:15)}));b["!cols"]=w;const e=g.utils.book_new();g.utils.book_append_sheet(e,b,r),g.writeFile(e,a)}const v={class:"g-excel-export"},z=["disabled"],O={key:0,class:"loading-icon"},P={key:1,class:"export-icon"},D=S(t.defineComponent({name:"GExcelExport",__name:"index",props:{buttonText:{default:"导出 Excel"},loadingText:{default:"导出中..."},buttonClass:{default:"g-export-button"},disabled:{type:Boolean,default:!1},exportOptions:{default:()=>({})},data:{},columns:{},fileName:{default:"export.xlsx"},sheetName:{default:"Sheet1"},autoWidth:{type:Boolean,default:!0}},emits:["beforeExport","afterExport"],setup(l,{expose:n,emit:i}){const a=l,r=i,u=t.ref(!1),s=async()=>{if(!(u.value||a.disabled))try{u.value=!0,r("beforeExport");const c={fileName:a.fileName,sheetName:a.sheetName,includeHeaders:!0,...a.exportOptions};await new Promise(p=>setTimeout(p,100)),y(a.data,a.columns,c),r("afterExport",!0)}catch(c){console.error("Export failed:",c),r("afterExport",!1,c)}finally{u.value=!1}};return n({export:s}),(c,p)=>(t.openBlock(),t.createElementBlock("div",v,[t.createElementVNode("button",{class:t.normalizeClass(c.buttonClass),disabled:c.disabled||u.value,onClick:s},[u.value?(t.openBlock(),t.createElementBlock("span",O,"⏳")):(t.openBlock(),t.createElementBlock("span",P,"📊")),t.createTextVNode(" "+t.toDisplayString(u.value?c.loadingText:c.buttonText),1)],10,z)]))}}),[["__scopeId","data-v-faaaa3c6"]]),V=[_,D],E=l=>{V.forEach(n=>{const i=n.name||n.__name||"UnknownComponent";l.component(i,n)})},j={install:E};d.GExcelExport=D,d.GTable=_,d.default=j,d.exportToExcel=y,d.install=E,Object.defineProperties(d,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
