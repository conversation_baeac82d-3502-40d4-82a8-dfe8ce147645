<template>
  <div class="d-flex justify-center align-center ga-2">
    <v-label style="width: 100px">default</v-label>

    <v-chip size="x-small">
      x-small
    </v-chip>

    <v-chip size="small">
      small
    </v-chip>

    <v-chip>
      default
    </v-chip>

    <v-chip size="large">
      large
    </v-chip>

    <v-chip size="x-large">
      x-large
    </v-chip>
  </div>
  <div class="d-flex justify-center align-center ga-2 mt-2">
    <v-label style="width: 100px">comfortable</v-label>

    <v-chip density="comfortable" size="x-small">
      x-small
    </v-chip>

    <v-chip density="comfortable" size="small">
      small
    </v-chip>

    <v-chip density="comfortable">
      default
    </v-chip>

    <v-chip density="comfortable" size="large">
      large
    </v-chip>

    <v-chip density="comfortable" size="x-large">
      x-large
    </v-chip>
  </div>
  <div class="d-flex justify-center align-center ga-2 mt-2">
    <v-label style="width: 100px">compact</v-label>

    <v-chip density="compact" size="x-small">
      x-small
    </v-chip>

    <v-chip density="compact" size="small">
      small
    </v-chip>

    <v-chip density="compact">
      default
    </v-chip>

    <v-chip density="compact" size="large">
      large
    </v-chip>

    <v-chip density="compact" size="x-large">
      x-large
    </v-chip>
  </div>
</template>
<script setup lang="ts">
</script>
