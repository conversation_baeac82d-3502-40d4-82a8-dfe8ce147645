<template>
  <v-container fluid>
    <p>{{ selected }}</p>
    <v-checkbox
      v-model="selected"
      label="<PERSON>"
      value="<PERSON>"
    ></v-checkbox>
    <v-checkbox
      v-model="selected"
      label="<PERSON>"
      value="<PERSON>"
    ></v-checkbox>
  </v-container>
</template>

<script setup>
  import { ref } from 'vue'

  const selected = ref(['<PERSON>'])
</script>

<script>
  export default {
    data () {
      return {
        selected: ['<PERSON>'],
      }
    },
  }
</script>
