<template>
  <v-row>
    <v-col>
      <v-sheet height="500">
        <v-calendar
          ref="calendar"
          v-model="value"
          type="week"
        >
          <template v-slot:day-body="{ date, week }">
            <div
              :class="{ first: date === week[0].date }"
              :style="{ top: nowY }"
              class="v-current-time"
            ></div>
          </template>
        </v-calendar>
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script setup>
  import { computed, onMounted, ref } from 'vue'

  const calendar = ref()

  const value = ref('')
  const ready = ref(false)

  const cal = computed(() => {
    return ready.value ? calendar.value : null
  })
  const nowY = computed(() => {
    return cal.value ? cal.value.timeToY(cal.value.times.now) + 'px' : '-10px'
  })

  onMounted(() => {
    ready.value = true
    scrollToTime()
    updateTime()
  })

  function getCurrentTime () {
    return cal.value ? cal.value.times.now.hour * 60 + cal.value.times.now.minute : 0
  }
  function scrollToTime () {
    const time = getCurrentTime()
    const first = Math.max(0, time - (time % 30) - 30)
    cal.value.scrollToTime(first)
  }
  function updateTime () {
    setInterval(() => cal.value.updateTimes(), 60 * 1000)
  }
</script>

<script>
  export default {
    data: () => ({
      value: '',
      ready: false,
    }),
    computed: {
      cal () {
        return this.ready ? this.$refs.calendar : null
      },
      nowY () {
        return this.cal ? this.cal.timeToY(this.cal.times.now) + 'px' : '-10px'
      },
    },
    mounted () {
      this.ready = true
      this.scrollToTime()
      this.updateTime()
    },
    methods: {
      getCurrentTime () {
        return this.cal ? this.cal.times.now.hour * 60 + this.cal.times.now.minute : 0
      },
      scrollToTime () {
        const time = this.getCurrentTime()
        const first = Math.max(0, time - (time % 30) - 30)

        this.cal.scrollToTime(first)
      },
      updateTime () {
        setInterval(() => this.cal.updateTimes(), 60 * 1000)
      },
    },
  }
</script>

<style lang="scss">
.v-current-time {
  height: 2px;
  background-color: #ea4335;
  position: absolute;
  left: -1px;
  right: 0;
  pointer-events: none;

  &.first::before {
    content: '';
    position: absolute;
    background-color: #ea4335;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-top: -5px;
    margin-left: -6.5px;
  }
}
</style>
