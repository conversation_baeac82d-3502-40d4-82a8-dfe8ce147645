<template>
  <div>
    <v-alert
      border="top"
      color="primary"
    >
      I'm an alert with a top border and primary color
    </v-alert>

    <br>

    <v-alert
      border="end"
      color="secondary"
    >
      I'm an alert with an end border and secondary color
    </v-alert>

    <br>

    <v-alert
      border="bottom"
      color="success"
    >
      I'm an alert with a bottom border and success color
    </v-alert>

    <br>

    <v-alert
      border="start"
      color="error"
    >
      I'm an alert with a start border and error color
    </v-alert>
  </div>
</template>
