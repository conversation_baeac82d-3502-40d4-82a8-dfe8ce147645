<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { GButton, GCard } from '@galaxy-vue/ui'
import { GTable, GExcelExport } from '@galaxy-vue/table'

// 测试数据
const tableColumns = [
  { field: 'name', headerName: '姓名', sortable: true },
  { field: 'age', headerName: '年龄', sortable: true },
  { field: 'email', headerName: '邮箱', filter: true },
  { field: 'department', headerName: '部门' }
]

const tableData = [
  { name: '张三', age: 30, email: 'zhang<PERSON>@example.com', department: '技术部' },
  { name: '李四', age: 25, email: '<EMAIL>', department: '产品部' },
  { name: '王五', age: 28, email: '<EMAIL>', department: '设计部' },
  { name: '赵六', age: 32, email: '<EMAIL>', department: '运营部' }
]

const handleButtonClick = () => {
  alert('Galaxy Vue UI 按钮点击成功！')
}

const handleExportComplete = (success: boolean, error?: Error) => {
  if (success) {
    alert('Excel 导出成功！')
  } else {
    alert(`Excel 导出失败：${error?.message}`)
  }
}
</script>

<template>
  <div id="app">
    <header>
      <img alt="Vue logo" class="logo" src="@/assets/logo.svg" width="125" height="125" />
      <div class="wrapper">
        <h1>Galaxy Vue 组件库测试</h1>
        <nav>
          <RouterLink to="/">Home</RouterLink>
          <RouterLink to="/about">About</RouterLink>
        </nav>
      </div>
    </header>

    <main class="main-content">
      <!-- UI 组件测试 -->
      <section class="test-section">
        <h2>@galaxy-vue/ui 组件测试</h2>

        <GCard header="按钮组件测试">
          <div class="button-group">
            <GButton type="primary" @click="handleButtonClick">Primary Button</GButton>
            <GButton type="success">Success Button</GButton>
            <GButton type="warning">Warning Button</GButton>
            <GButton type="danger">Danger Button</GButton>
          </div>

          <div class="button-group">
            <GButton plain>Plain Button</GButton>
            <GButton round>Round Button</GButton>
            <GButton loading>Loading Button</GButton>
            <GButton disabled>Disabled Button</GButton>
          </div>

          <template #footer>
            <p>✅ Galaxy Vue UI 组件加载成功！</p>
          </template>
        </GCard>
      </section>

      <!-- Table 组件测试 -->
      <section class="test-section">
        <h2>@galaxy-vue/table 组件测试</h2>

        <GCard header="表格组件测试">
          <div class="table-controls">
            <GExcelExport
              :data="tableData"
              :columns="tableColumns"
              fileName="员工数据.xlsx"
              @after-export="handleExportComplete"
            />
          </div>

          <GTable
            :columnDefs="tableColumns"
            :rowData="tableData"
            height="300px"
            :pagination="true"
            :paginationPageSize="10"
          />

          <template #footer>
            <p>✅ Galaxy Vue Table 组件加载成功！</p>
          </template>
        </GCard>
      </section>

      <RouterView />
    </main>
  </div>
</template>

<style scoped>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

header {
  line-height: 1.5;
  max-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.logo {
  display: block;
  margin: 0 auto 1rem;
}

.wrapper h1 {
  margin: 1rem 0;
  font-size: 2rem;
  font-weight: bold;
}

nav {
  width: 100%;
  font-size: 14px;
  text-align: center;
  margin-top: 1rem;
}

nav a {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0 0.5rem;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

nav a:hover,
nav a.router-link-exact-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  color: #409eff;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.button-group {
  margin-bottom: 20px;
}

.button-group .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.table-controls {
  margin-bottom: 20px;
  text-align: right;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding: 20px 40px;
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: center;
    justify-content: space-between;
    width: 100%;
  }

  nav {
    text-align: right;
    margin: 0;
  }
}
</style>
