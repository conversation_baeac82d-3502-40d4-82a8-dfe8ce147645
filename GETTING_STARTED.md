# Galaxy Vue - 快速开始指南

## 项目概述

Galaxy Vue 是一个基于 Vue 3 的现代组件库脚手架，包含两个主要包：

- **@galaxy-vue/ui** - 基于 Element Plus 的二次封装组件库
- **@galaxy-vue/table** - 结合 AG Grid 和 Excel 导出功能的高级表格组件

## 项目结构

```
galaxy-vue/
├── packages/
│   ├── ui/                     # @galaxy-vue/ui 包
│   │   ├── src/
│   │   │   ├── components/     # 组件目录
│   │   │   │   ├── GButton/    # 按钮组件
│   │   │   │   └── GCard/      # 卡片组件
│   │   │   ├── index.ts        # 主入口文件
│   │   │   ├── main.ts         # 开发入口
│   │   │   └── App.vue         # 开发示例页面
│   │   ├── package.json
│   │   ├── vite.config.ts
│   │   └── tsconfig.json
│   └── table/                  # @galaxy-vue/table 包
│       ├── src/
│       │   ├── components/     # 组件目录
│       │   │   ├── GTable/     # 表格组件
│       │   │   └── GExcelExport/ # Excel导出组件
│       │   ├── utils/          # 工具函数
│       │   │   └── excel.ts    # Excel处理工具
│       │   ├── types/          # 类型定义
│       │   └── index.ts        # 主入口文件
│       ├── package.json
│       ├── vite.config.ts
│       └── tsconfig.json
├── package.json                # 根包配置
├── pnpm-workspace.yaml         # pnpm workspace 配置
├── lerna.json                  # lerna 配置
├── tsconfig.json               # TypeScript 配置
└── README.md                   # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 开发模式

启动 UI 组件开发服务器：
```bash
pnpm dev
```

访问 http://localhost:5173 查看组件示例。

### 3. 构建

构建所有包：
```bash
pnpm build
```

构建单个包：
```bash
pnpm build:ui      # 构建 UI 包
pnpm build:table   # 构建 Table 包
```

## 组件使用示例

### UI 组件

```vue
<template>
  <div>
    <GButton type="primary" @click="handleClick">
      点击我
    </GButton>
    
    <GCard header="卡片标题">
      <p>这是卡片内容</p>
      <template #footer>
        <GButton size="small">操作</GButton>
      </template>
    </GCard>
  </div>
</template>

<script setup>
import { GButton, GCard } from '@galaxy-vue/ui'

const handleClick = () => {
  console.log('按钮被点击了')
}
</script>
```

### Table 组件

```vue
<template>
  <div>
    <GTable
      :columnDefs="columns"
      :rowData="data"
      height="400px"
      @selection-changed="onSelectionChanged"
    />
    
    <GExcelExport
      :data="data"
      :columns="columns"
      fileName="数据导出.xlsx"
      @after-export="onExportComplete"
    />
  </div>
</template>

<script setup>
import { GTable, GExcelExport } from '@galaxy-vue/table'

const columns = [
  { field: 'name', headerName: '姓名', sortable: true },
  { field: 'age', headerName: '年龄', sortable: true },
  { field: 'email', headerName: '邮箱', filter: true }
]

const data = [
  { name: '张三', age: 30, email: '<EMAIL>' },
  { name: '李四', age: 25, email: '<EMAIL>' }
]

const onSelectionChanged = (selectedRows) => {
  console.log('选中的行:', selectedRows)
}

const onExportComplete = (success, error) => {
  if (success) {
    console.log('导出成功')
  } else {
    console.error('导出失败:', error)
  }
}
</script>
```

## 开发指南

### 添加新组件

1. 在对应包的 `src/components/` 目录下创建组件文件夹
2. 创建 `index.vue` 文件编写组件
3. 在 `src/index.ts` 中导出组件
4. 更新 `src/App.vue` 添加组件示例

### 发布包

```bash
# 更新版本
lerna version

# 发布到 npm
lerna publish
```

## 技术栈

- **Vue 3** - 前端框架
- **TypeScript** - 类型支持
- **Element Plus** - 基础 UI 组件库
- **AG Grid** - 高性能表格组件
- **XLSX** - Excel 文件处理
- **Vite** - 构建工具
- **Lerna** - Monorepo 管理
- **pnpm** - 包管理器

## 下一步

1. 根据需求添加更多组件
2. 完善组件文档和示例
3. 添加单元测试
4. 设置 CI/CD 流程
5. 发布到 npm 仓库

## 支持

如有问题，请提交 Issue 或联系开发团队。
